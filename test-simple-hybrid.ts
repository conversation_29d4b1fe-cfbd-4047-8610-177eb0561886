import { createHybridWordProcessor } from './src/utils/word-export/hybrid-word-processor';

async function testHybridProcessor() {
  console.log('=== 测试混合处理器基本功能 ===');
  
  try {
    const processor = createHybridWordProcessor();
    
    const htmlContent = `
      <html>
        <body>
          <h1>测试</h1>
          <p>这是一个图片：</p>
          <img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==" width="50" height="50" alt="测试图片">
        </body>
      </html>
    `;
    
    console.log('1. 测试HTML预处理...');
    const processedHtml = processor.preprocessHtml(htmlContent, {
      processImages: true,
      debug: true,
    });
    
    console.log('预处理结果:');
    console.log(processedHtml);
    
    console.log('2. 测试后处理（模拟）...');
    const mockBuffer = Buffer.from('mock docx content');
    const result = await processor.postProcessWordDocument(mockBuffer, {
      processImages: true,
      debug: true,
    });
    
    console.log('后处理完成，结果长度:', result.length);
    
    processor.clear();
    console.log('=== 测试完成 ===');
    
  } catch (error) {
    console.error('=== 测试失败 ===');
    console.error(error);
  }
}

testHybridProcessor();
