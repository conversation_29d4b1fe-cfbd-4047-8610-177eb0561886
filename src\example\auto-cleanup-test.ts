/**
 * 自动清理临时文件测试示例
 * 验证执行完成后自动删除临时文件的功能
 */

import { WordExportService } from '../service/word-export.service';
import { FileManagerService } from '../service/file-manager.service';
import * as fs from 'fs';

/**
 * 测试自动清理功能
 */
async function testAutoCleanup() {
  console.log('🧹 开始自动清理功能测试...');

  const fileManagerService = new FileManagerService();
  const wordExportService = new WordExportService();
  // 手动注入依赖
  wordExportService.fileManagerService = fileManagerService;

  // 模拟HTML内容
  const htmlContent = `
    <html>
      <head><title>自动清理测试文档</title></head>
      <body>
        <h1>自动清理测试</h1>
        <p>这是一个用于测试自动清理功能的文档。</p>
        <p style="text-emphasis: filled currentColor; text-emphasis-position: under right;">
          这段文字有着重号，会产生调试文件。
        </p>
        <table border="1">
          <tr><th>测试项</th><th>状态</th></tr>
          <tr><td>自动清理</td><td>测试中</td></tr>
        </table>
      </body>
    </html>
  `;

  const sessionId = fileManagerService.generateSessionId();
  console.log(`📋 测试会话ID: ${sessionId}`);

  try {
    // 获取初始统计信息
    const initialStats = fileManagerService.getSessionStats();
    console.log('📊 初始会话统计:', initialStats);

    // 执行导出（启用自动清理）
    console.log('🚀 开始导出（启用自动清理）...');
    const docxBuffer = await wordExportService.exportHtmlToWord(htmlContent, {
      title: '自动清理测试文档',
      author: '测试系统',
      sessionId: sessionId,
      autoCleanup: true, // 启用自动清理
    });

    console.log(`✅ 导出完成，文档大小: ${docxBuffer.length} 字节`);

    // 立即检查临时文件
    const immediateFiles = fileManagerService.getSessionFiles(sessionId);
    console.log(
      `📁 导出完成后立即检查，临时文件数量: ${immediateFiles.length}`
    );
    immediateFiles.forEach((file, index) => {
      const exists = fs.existsSync(file);
      console.log(
        `  文件${index + 1}: ${exists ? '存在' : '不存在'} - ${file}`
      );
    });

    // 等待自动清理完成（5秒后）
    console.log('⏳ 等待自动清理完成（6秒）...');
    await new Promise(resolve => setTimeout(resolve, 6000));

    // 检查清理后的状态
    const finalFiles = fileManagerService.getSessionFiles(sessionId);
    console.log(`🧹 自动清理后，临时文件数量: ${finalFiles.length}`);

    if (finalFiles.length === 0) {
      console.log('✅ 自动清理成功！所有临时文件已删除');
    } else {
      console.log('❌ 自动清理失败，仍有临时文件存在:');
      finalFiles.forEach((file, index) => {
        const exists = fs.existsSync(file);
        console.log(
          `  文件${index + 1}: ${exists ? '存在' : '不存在'} - ${file}`
        );
      });
    }

    // 获取最终统计信息
    const finalStats = fileManagerService.getSessionStats();
    console.log('📊 最终会话统计:', finalStats);

    return {
      success: finalFiles.length === 0,
      sessionId,
      initialFileCount: immediateFiles.length,
      finalFileCount: finalFiles.length,
      docxSize: docxBuffer.length,
    };
  } catch (error) {
    console.error('❌ 自动清理测试失败:', error);
    throw error;
  }
}

/**
 * 测试手动清理功能
 */
async function testManualCleanup() {
  console.log('\n🔧 开始手动清理功能测试...');

  const fileManagerService = new FileManagerService();
  const wordExportService = new WordExportService();
  // 手动注入依赖
  wordExportService.fileManagerService = fileManagerService;

  const htmlContent = `
    <html>
      <head><title>手动清理测试文档</title></head>
      <body>
        <h1>手动清理测试</h1>
        <p>这是一个用于测试手动清理功能的文档。</p>
      </body>
    </html>
  `;

  const sessionId = fileManagerService.generateSessionId();
  console.log(`📋 测试会话ID: ${sessionId}`);

  try {
    // 执行导出（禁用自动清理）
    console.log('🚀 开始导出（禁用自动清理）...');
    const docxBuffer = await wordExportService.exportHtmlToWord(htmlContent, {
      title: '手动清理测试文档',
      author: '测试系统',
      sessionId: sessionId,
      autoCleanup: false, // 禁用自动清理
    });

    console.log(`✅ 导出完成，文档大小: ${docxBuffer.length} 字节`);

    // 检查临时文件
    const filesBeforeCleanup = fileManagerService.getSessionFiles(sessionId);
    console.log(
      `📁 禁用自动清理后，临时文件数量: ${filesBeforeCleanup.length}`
    );

    if (filesBeforeCleanup.length > 0) {
      console.log('✅ 临时文件保留成功（自动清理已禁用）');

      // 手动清理
      console.log('🧹 执行手动清理...');
      await fileManagerService.cleanupSessionFiles(sessionId);

      // 检查清理结果
      const filesAfterCleanup = fileManagerService.getSessionFiles(sessionId);
      console.log(`🧹 手动清理后，临时文件数量: ${filesAfterCleanup.length}`);

      if (filesAfterCleanup.length === 0) {
        console.log('✅ 手动清理成功！');
      } else {
        console.log('❌ 手动清理失败');
      }
    } else {
      console.log('⚠️  没有临时文件需要清理');
    }

    return {
      success: true,
      sessionId,
      filesBeforeCleanup: filesBeforeCleanup.length,
      filesAfterCleanup: fileManagerService.getSessionFiles(sessionId).length,
    };
  } catch (error) {
    console.error('❌ 手动清理测试失败:', error);
    throw error;
  }
}

/**
 * 测试批量清理功能
 */
async function testBatchCleanup() {
  console.log('\n📦 开始批量清理功能测试...');

  const fileManagerService = new FileManagerService();
  const wordExportService = new WordExportService();
  // 手动注入依赖
  wordExportService.fileManagerService = fileManagerService;

  const htmlContent = `<html><body><h1>批量清理测试</h1></body></html>`;
  const sessionIds: string[] = [];

  try {
    // 创建多个会话的临时文件
    console.log('🚀 创建3个会话的临时文件...');
    for (let i = 0; i < 3; i++) {
      const sessionId = fileManagerService.generateSessionId();
      sessionIds.push(sessionId);

      await wordExportService.exportHtmlToWord(htmlContent, {
        title: `批量测试文档${i + 1}`,
        sessionId: sessionId,
        autoCleanup: false, // 禁用自动清理
      });

      console.log(`  会话${i + 1}: ${sessionId}`);
    }

    // 检查所有会话的文件
    const statsBeforeCleanup = fileManagerService.getSessionStats();
    console.log('📊 批量清理前统计:', statsBeforeCleanup);

    // 执行批量清理
    console.log('🧹 执行批量清理...');
    await fileManagerService.cleanupMultipleSessions(sessionIds);

    // 检查清理结果
    const statsAfterCleanup = fileManagerService.getSessionStats();
    console.log('📊 批量清理后统计:', statsAfterCleanup);

    const success = statsAfterCleanup.totalFiles === 0;
    if (success) {
      console.log('✅ 批量清理成功！');
    } else {
      console.log('❌ 批量清理失败');
    }

    return {
      success,
      sessionIds,
      filesBeforeCleanup: statsBeforeCleanup.totalFiles,
      filesAfterCleanup: statsAfterCleanup.totalFiles,
    };
  } catch (error) {
    console.error('❌ 批量清理测试失败:', error);
    throw error;
  }
}

/**
 * 主测试函数
 */
async function main() {
  try {
    console.log('🎯 自动清理功能测试开始');
    console.log('='.repeat(60));

    // 测试自动清理
    const autoCleanupResult = await testAutoCleanup();

    // 测试手动清理
    const manualCleanupResult = await testManualCleanup();

    // 测试批量清理
    const batchCleanupResult = await testBatchCleanup();

    console.log('\n' + '='.repeat(60));
    console.log('📋 测试结果汇总:');
    console.log(
      `  自动清理: ${autoCleanupResult.success ? '✅ 通过' : '❌ 失败'}`
    );
    console.log(
      `  手动清理: ${manualCleanupResult.success ? '✅ 通过' : '❌ 失败'}`
    );
    console.log(
      `  批量清理: ${batchCleanupResult.success ? '✅ 通过' : '❌ 失败'}`
    );

    const allTestsPassed =
      autoCleanupResult.success &&
      manualCleanupResult.success &&
      batchCleanupResult.success;

    if (allTestsPassed) {
      console.log('\n🎉 所有测试通过！自动清理功能工作正常。');
    } else {
      console.log('\n❌ 部分测试失败，请检查日志。');
    }
  } catch (error) {
    console.error('❌ 测试失败:', error);
    process.exit(1);
  }
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
  main();
}

export {
  testAutoCleanup,
  testManualCleanup,
  testBatchCleanup,
  main as runAutoCleanupTest,
};
