/**
 * 清理测试文件脚本
 * 用于清理测试过程中产生的临时文件
 */

import { FileManagerService } from '../service/file-manager.service';
import * as fs from 'fs';
import * as path from 'path';

/**
 * 清理所有测试文件
 */
async function cleanupAllTestFiles() {
  console.log('🧹 开始清理所有测试文件...');

  const fileManagerService = new FileManagerService();

  try {
    // 获取当前统计信息
    const stats = fileManagerService.getSessionStats();
    console.log('📊 清理前统计:', stats);

    if (stats.totalFiles === 0) {
      console.log('✅ 没有需要清理的会话文件');
    } else {
      // 清理所有会话文件
      await fileManagerService.cleanupAllSessionFiles();
      console.log(
        `✅ 已清理 ${stats.totalSessions} 个会话的 ${stats.totalFiles} 个文件`
      );
    }

    // 清理目录中的遗留文件
    await cleanupDirectoryFiles(
      fileManagerService.getWordExportsDir(),
      '*.docx'
    );
    await cleanupDirectoryFiles(
      fileManagerService.getDebugFilesDir(),
      '*.html'
    );
    await cleanupDirectoryFiles(
      fileManagerService.getParseResultsDir(),
      '*.json'
    );

    console.log('🎉 所有测试文件清理完成！');
  } catch (error) {
    console.error('❌ 清理测试文件失败:', error);
    throw error;
  }
}

/**
 * 清理指定目录中的文件
 * @param directory 目录路径
 * @param pattern 文件模式（用于日志显示）
 */
async function cleanupDirectoryFiles(directory: string, pattern: string) {
  try {
    if (!fs.existsSync(directory)) {
      console.log(`📁 目录不存在: ${directory}`);
      return;
    }

    const files = await fs.promises.readdir(directory);
    if (files.length === 0) {
      console.log(`📁 目录为空: ${directory}`);
      return;
    }

    console.log(`🧹 清理目录: ${directory} (${files.length} 个文件)`);

    let deletedCount = 0;
    let errorCount = 0;

    for (const file of files) {
      const filePath = path.join(directory, file);
      try {
        const stats = await fs.promises.stat(filePath);
        if (stats.isFile()) {
          await fs.promises.unlink(filePath);
          deletedCount++;
          console.log(`  ✅ 删除: ${file}`);
        }
      } catch (error) {
        errorCount++;
        console.warn(`  ❌ 删除失败: ${file}`, error.message);
      }
    }

    console.log(
      `📊 目录清理完成: 成功删除 ${deletedCount} 个文件, 失败 ${errorCount} 个文件`
    );
  } catch (error) {
    console.error(`清理目录失败: ${directory}`, error);
  }
}

/**
 * 显示当前文件状态
 */
async function showCurrentStatus() {
  console.log('📋 当前临时文件状态:');

  const fileManagerService = new FileManagerService();

  // 显示会话统计
  const stats = fileManagerService.getSessionStats();
  console.log('📊 会话统计:', stats);

  // 显示各目录文件数量
  const directories = [
    { name: 'Word导出', path: fileManagerService.getWordExportsDir() },
    { name: '调试文件', path: fileManagerService.getDebugFilesDir() },
    { name: '解析结果', path: fileManagerService.getParseResultsDir() },
  ];

  for (const dir of directories) {
    try {
      if (fs.existsSync(dir.path)) {
        const files = await fs.promises.readdir(dir.path);
        console.log(`📁 ${dir.name}: ${files.length} 个文件`);
        if (files.length > 0 && files.length <= 10) {
          files.forEach(file => console.log(`  - ${file}`));
        } else if (files.length > 10) {
          files.slice(0, 5).forEach(file => console.log(`  - ${file}`));
          console.log(`  ... 还有 ${files.length - 5} 个文件`);
        }
      } else {
        console.log(`📁 ${dir.name}: 目录不存在`);
      }
    } catch (error) {
      console.error(`检查目录失败: ${dir.path}`, error);
    }
  }
}

/**
 * 主函数
 */
async function main() {
  const args = process.argv.slice(2);
  const command = args[0] || 'status';

  try {
    switch (command) {
      case 'status':
        await showCurrentStatus();
        break;

      case 'clean':
        await cleanupAllTestFiles();
        break;

      case 'clean-and-status':
        await cleanupAllTestFiles();
        console.log('\n' + '='.repeat(50));
        await showCurrentStatus();
        break;

      default:
        console.log('使用方法:');
        console.log(
          '  node --require ts-node/register src/example/cleanup-test-files.ts [command]'
        );
        console.log('');
        console.log('命令:');
        console.log('  status          显示当前文件状态（默认）');
        console.log('  clean           清理所有测试文件');
        console.log('  clean-and-status 清理文件并显示状态');
        break;
    }
  } catch (error) {
    console.error('❌ 执行失败:', error);
    process.exit(1);
  }
}

// 如果直接运行此文件，执行主函数
if (require.main === module) {
  main();
}

export {
  cleanupAllTestFiles,
  cleanupDirectoryFiles,
  showCurrentStatus,
  main as runCleanupScript,
};
