import { WordExportService } from './src/service/word-export.service';
import { FileManagerService } from './src/service/file-manager.service';
import * as fs from 'fs';
import * as path from 'path';

async function testImageOnly() {
  console.log('开始测试图片显示功能...');
  
  try {
    // 读取测试HTML文件
    const htmlPath = path.join(process.cwd(), 'test-image-only.html');
    const htmlContent = fs.readFileSync(htmlPath, 'utf-8');
    
    console.log('已读取测试HTML文件');
    console.log('HTML内容长度:', htmlContent.length);
    
    // 创建服务实例并手动注入依赖
    const fileManagerService = new FileManagerService();
    const wordExportService = new WordExportService();
    wordExportService.fileManagerService = fileManagerService;
    
    // 转换为Word
    const outputPath = path.join(process.cwd(), 'test-image-only-result.docx');
    await wordExportService.exportHtmlToWordFile(htmlContent, outputPath, {
      title: '图片显示测试',
      author: '测试系统',
    });
    
    console.log(`Word文档已生成: ${outputPath}`);
    console.log('请打开Word文档查看图片是否正确显示');
    
  } catch (error) {
    console.error('测试失败:', error);
  }
}

testImageOnly();
