import { WordExportService } from './src/service/word-export.service';
import { FileManagerService } from './src/service/file-manager.service';
import * as fs from 'fs';
import * as path from 'path';

async function testFullImageSupport() {
  console.log('=== 开始测试完整图片支持 ===');
  
  try {
    // 创建一个包含图片的HTML
    const htmlContent = `
      <!DOCTYPE html>
      <html>
      <head>
          <meta charset="UTF-8">
          <title>完整图片支持测试</title>
      </head>
      <body>
          <h1>完整图片支持测试</h1>
          
          <h2>测试1：小图片</h2>
          <p>这是一个红色的小图片：</p>
          <img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==" width="50" height="50" alt="红色像素">
          
          <h2>测试2：中等图片</h2>
          <p>这是一个蓝色的中等图片：</p>
          <img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==" width="100" height="100" alt="蓝色像素">
          
          <h2>测试3：表格中的图片</h2>
          <table border="1">
              <tr>
                  <td>图片1</td>
                  <td><img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==" width="30" height="30" alt="表格图片1"></td>
              </tr>
              <tr>
                  <td>图片2</td>
                  <td><img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==" width="30" height="30" alt="表格图片2"></td>
              </tr>
          </table>
          
          <h2>测试4：混合内容</h2>
          <p>这个段落包含<span data-emphasis-mark="dot">着重号</span>、<span style="text-decoration: underline wavy;">波浪线</span>和图片：<img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==" width="20" height="20" alt="内联图片">。</p>
          
          <p>测试完成。</p>
      </body>
      </html>
    `;
    
    console.log('已创建测试HTML内容');
    
    // 创建服务实例并手动注入依赖
    const fileManagerService = new FileManagerService();
    const wordExportService = new WordExportService();
    wordExportService.fileManagerService = fileManagerService;
    
    // 测试1：不启用完整图片支持（当前默认行为）
    console.log('\n--- 测试1：默认图片处理（占位符） ---');
    const outputPath1 = path.join(process.cwd(), 'test-image-placeholder.docx');
    await wordExportService.exportHtmlToWordFile(htmlContent, outputPath1, {
      title: '图片占位符测试',
      author: '测试系统',
      enableImageSupport: false, // 明确禁用
    });
    console.log(`占位符版本已生成: ${outputPath1}`);
    
    // 测试2：启用完整图片支持
    console.log('\n--- 测试2：完整图片支持 ---');
    const outputPath2 = path.join(process.cwd(), 'test-image-full-support.docx');
    await wordExportService.exportHtmlToWordFile(htmlContent, outputPath2, {
      title: '完整图片支持测试',
      author: '测试系统',
      enableImageSupport: true, // 启用完整图片支持
    });
    console.log(`完整图片支持版本已生成: ${outputPath2}`);
    
    console.log('\n=== 测试完成 ===');
    console.log('请打开以下文件对比效果：');
    console.log(`1. 占位符版本: ${outputPath1}`);
    console.log(`2. 完整图片版本: ${outputPath2}`);
    
  } catch (error) {
    console.error('=== 测试失败 ===');
    console.error('错误信息:', error);
    console.error('错误堆栈:', error.stack);
  }
}

console.log('=== 启动完整图片支持测试 ===');
testFullImageSupport().then(() => {
  console.log('=== 测试流程完成 ===');
}).catch(error => {
  console.error('=== 测试异常 ===');
  console.error(error);
});
