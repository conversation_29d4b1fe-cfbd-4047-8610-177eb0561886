<!DOCTYPE html><html><head>
    <meta charset="UTF-8">
    <title>简单图片测试</title>
</head>
<body>
    <h1>图片显示测试</h1>

    <h2>测试1：简单文本</h2>
    <p>这是一段普通文本，用来确认基本转换功能正常。</p>

    <h2>测试2：小的base64图片</h2>
    <p>下面应该有一个红色的1x1像素图片：</p>
    <img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==" width="50" height="50" alt="红色像素" style="; max-width: 450px; height: auto;">

    <h2>测试3：带样式的图片</h2>
    <p>下面是一个带样式的图片：</p>
    <img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==" style="width: 50px; height: 50px; border: 1px solid black;; max-width: 450px; height: auto;" alt="蓝色像素">

    <h2>测试4：无尺寸图片</h2>
    <p>下面是一个没有指定尺寸的图片：</p>
    <img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==" alt="无尺寸图片" style="; max-width: 450px; height: auto;">

    <p>测试完成。</p>

</body></html>