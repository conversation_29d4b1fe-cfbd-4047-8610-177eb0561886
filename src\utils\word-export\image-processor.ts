/**
 * 图片处理器
 * 用于处理HTML中的图片，为Word文档生成做准备
 * 
 * 注意：由于html-to-docx库对base64图片支持有限，
 * 当前版本将图片转换为占位符文本。
 * 
 * 未来可以扩展使用docx库来实现完整的图片支持。
 */

export interface ImageInfo {
  id: string;
  src: string;
  alt: string;
  width?: number;
  height?: number;
  type: 'base64' | 'url';
  mimeType?: string;
  data?: string; // base64数据部分
}

export class ImageProcessor {
  private images: ImageInfo[] = [];

  /**
   * 从HTML中提取图片信息
   */
  extractImages(htmlContent: string): ImageInfo[] {
    const parser = new DOMParser();
    const doc = parser.parseFromString(htmlContent, 'text/html');
    const imgElements = doc.querySelectorAll('img');
    
    this.images = [];
    
    imgElements.forEach((img, index) => {
      const htmlImg = img as HTMLImageElement;
      const src = htmlImg.src;
      
      if (src) {
        const imageInfo: ImageInfo = {
          id: `IMAGE_${index}_${Date.now()}`,
          src: src,
          alt: htmlImg.alt || '图片',
          width: htmlImg.width || undefined,
          height: htmlImg.height || undefined,
          type: src.startsWith('data:image/') ? 'base64' : 'url'
        };
        
        // 解析base64图片信息
        if (imageInfo.type === 'base64') {
          const match = src.match(/^data:image\/([^;]+);base64,(.+)$/);
          if (match) {
            imageInfo.mimeType = match[1];
            imageInfo.data = match[2];
          }
        }
        
        this.images.push(imageInfo);
      }
    });
    
    return this.images;
  }

  /**
   * 获取所有提取的图片信息
   */
  getImages(): ImageInfo[] {
    return this.images;
  }

  /**
   * 将base64数据转换为Buffer（用于docx库）
   */
  base64ToBuffer(base64Data: string): Buffer {
    return Buffer.from(base64Data, 'base64');
  }

  /**
   * 计算合适的图片尺寸（保持比例，不超过页面宽度）
   */
  calculateImageSize(originalWidth: number, originalHeight: number, maxWidth: number = 450): { width: number; height: number } {
    if (originalWidth <= maxWidth) {
      return { width: originalWidth, height: originalHeight };
    }
    
    const ratio = maxWidth / originalWidth;
    return {
      width: maxWidth,
      height: Math.round(originalHeight * ratio)
    };
  }

  /**
   * 生成图片占位符文本
   */
  generatePlaceholder(imageInfo: ImageInfo): string {
    const sizeInfo = imageInfo.width && imageInfo.height 
      ? ` (${imageInfo.width}x${imageInfo.height})`
      : '';
    
    return `[图片: ${imageInfo.alt}${sizeInfo}]`;
  }

  /**
   * 未来扩展：使用docx库插入图片
   * 这个方法为将来的完整图片支持预留接口
   */
  async insertImageWithDocx(imageInfo: ImageInfo): Promise<any> {
    // TODO: 实现使用docx库插入图片的逻辑
    // 这需要安装docx库并实现相应的图片插入功能
    
    console.log('未来功能：使用docx库插入图片', {
      id: imageInfo.id,
      type: imageInfo.type,
      alt: imageInfo.alt,
      size: imageInfo.width && imageInfo.height ? `${imageInfo.width}x${imageInfo.height}` : '未指定'
    });
    
    throw new Error('图片插入功能尚未实现，需要使用docx库');
  }

  /**
   * 清理图片缓存
   */
  clear(): void {
    this.images = [];
  }
}

/**
 * 创建图片处理器实例
 */
export function createImageProcessor(): ImageProcessor {
  return new ImageProcessor();
}
