/**
 * 图片处理器
 * 用于处理HTML中的图片，为Word文档生成做准备
 * 
 * 注意：由于html-to-docx库对base64图片支持有限，
 * 当前版本将图片转换为占位符文本。
 * 
 * 未来可以扩展使用docx库来实现完整的图片支持。
 */

export interface ImageInfo {
  id: string;
  src: string;
  alt: string;
  width?: number;
  height?: number;
  type: 'base64' | 'url';
  mimeType?: string;
  data?: string; // base64数据部分
}

export class ImageProcessor {
  private images: ImageInfo[] = [];

  /**
   * 从HTML中提取图片信息
   */
  extractImages(htmlContent: string): ImageInfo[] {
    // 使用正则表达式提取图片信息（避免依赖DOM解析器）
    const imgRegex = /<img[^>]*>/gi;
    const matches = htmlContent.match(imgRegex) || [];

    this.images = [];

    matches.forEach((imgTag, index) => {
      // 提取src属性
      const srcMatch = imgTag.match(/src\s*=\s*["']([^"']+)["']/i);
      if (!srcMatch) return;

      const src = srcMatch[1];

      // 提取alt属性
      const altMatch = imgTag.match(/alt\s*=\s*["']([^"']*)["']/i);
      const alt = altMatch ? altMatch[1] : '图片';

      // 提取width属性
      const widthMatch = imgTag.match(/width\s*=\s*["']?(\d+)["']?/i);
      const width = widthMatch ? parseInt(widthMatch[1]) : undefined;

      // 提取height属性
      const heightMatch = imgTag.match(/height\s*=\s*["']?(\d+)["']?/i);
      const height = heightMatch ? parseInt(heightMatch[1]) : undefined;

      const imageInfo: ImageInfo = {
        id: `IMAGE_${index}_${Date.now()}`,
        src: src,
        alt: alt,
        width: width,
        height: height,
        type: src.startsWith('data:image/') ? 'base64' : 'url'
      };

      // 解析base64图片信息
      if (imageInfo.type === 'base64') {
        const match = src.match(/^data:image\/([^;]+);base64,(.+)$/);
        if (match) {
          imageInfo.mimeType = match[1];
          imageInfo.data = match[2];
        }
      }

      this.images.push(imageInfo);
    });

    return this.images;
  }

  /**
   * 获取所有提取的图片信息
   */
  getImages(): ImageInfo[] {
    return this.images;
  }

  /**
   * 将base64数据转换为Buffer（用于docx库）
   */
  base64ToBuffer(base64Data: string): Buffer {
    return Buffer.from(base64Data, 'base64');
  }

  /**
   * 计算合适的图片尺寸（保持比例，不超过页面宽度）
   */
  calculateImageSize(originalWidth: number, originalHeight: number, maxWidth: number = 450): { width: number; height: number } {
    if (originalWidth <= maxWidth) {
      return { width: originalWidth, height: originalHeight };
    }
    
    const ratio = maxWidth / originalWidth;
    return {
      width: maxWidth,
      height: Math.round(originalHeight * ratio)
    };
  }

  /**
   * 生成图片占位符文本
   */
  generatePlaceholder(imageInfo: ImageInfo): string {
    const sizeInfo = imageInfo.width && imageInfo.height 
      ? ` (${imageInfo.width}x${imageInfo.height})`
      : '';
    
    return `[图片: ${imageInfo.alt}${sizeInfo}]`;
  }

  /**
   * 使用docx库插入图片
   * 将base64图片转换为docx可用的格式
   */
  async insertImageWithDocx(imageInfo: ImageInfo): Promise<any> {
    // 暂时禁用docx库的直接使用，因为它需要复杂的文档结构
    // 这个方法为将来的完整实现预留

    console.log('图片插入功能（docx库）暂未完全实现', {
      id: imageInfo.id,
      type: imageInfo.type,
      alt: imageInfo.alt,
      size: imageInfo.width && imageInfo.height ? `${imageInfo.width}x${imageInfo.height}` : '未指定'
    });

    // 返回占位符信息
    return {
      id: imageInfo.id,
      placeholder: this.generatePlaceholder(imageInfo),
      data: imageInfo.data,
      width: imageInfo.width,
      height: imageInfo.height
    };
  }

  /**
   * 清理图片缓存
   */
  clear(): void {
    this.images = [];
  }
}

/**
 * 创建图片处理器实例
 */
export function createImageProcessor(): ImageProcessor {
  return new ImageProcessor();
}
