import { Provide, Inject } from '@midwayjs/core';
import * as fs from 'fs';
import * as path from 'path';
import * as HTMLtoDOCX from 'html-to-docx';
import { v4 as uuidv4 } from 'uuid';
import {
  preprocessHtml,
  PreprocessOptions,
} from '../utils/word-export/html-preprocessor';
import {
  saveProcessedHtml,
  logConversionInfo,
} from '../utils/word-export/debug-helper';
import { patchWordDocument } from '../utils/word-export/word-xml-patcher-v2';
import { createDocxImageProcessor } from '../utils/word-export/docx-image-processor';
import { FileManagerService } from './file-manager.service';

/**
 * Word导出服务
 * 提供将HTML内容转换为Word文档的功能
 */
@Provide()
export class WordExportService {
  private readonly OUTPUT_DIR = path.join(
    process.cwd(),
    'temp',
    'word-exports'
  );

  // 默认页边距（单位：缇，1厘米 = 567缇）
  private readonly DEFAULT_MARGINS = {
    top: 1440, // 2.54厘米
    right: 1440, // 2.54厘米
    bottom: 1440, // 2.54厘米
    left: 1440, // 2.54厘米
  };

  @Inject()
  fileManagerService: FileManagerService;

  constructor() {
    // 确保输出目录存在
    if (!fs.existsSync(this.OUTPUT_DIR)) {
      fs.mkdirSync(this.OUTPUT_DIR, { recursive: true });
    }
  }

  /**
   * 生成唯一的文件名，避免并发冲突
   * @param baseName 基础文件名
   * @param extension 文件扩展名
   * @param sessionId 会话ID（可选）
   * @returns 唯一文件名
   */
  private generateUniqueFileName(
    baseName: string,
    extension: string,
    sessionId?: string
  ): string {
    const timestamp = Date.now();
    const uuid = uuidv4().substring(0, 8);
    const sessionPrefix = sessionId ? `${sessionId}_` : '';
    return `${sessionPrefix}${baseName}_${timestamp}_${uuid}.${extension}`;
  }

  /**
   * 将HTML内容转换为Word文档
   * @param htmlContent HTML内容
   * @param options 导出选项
   * @returns Word文档的Buffer
   */
  async exportHtmlToWord(
    htmlContent: string,
    options: {
      title?: string;
      author?: string;
      sessionId?: string;
      autoCleanup?: boolean; // 新增：是否自动清理临时文件
      margins?: {
        top?: number;
        right?: number;
        bottom?: number;
        left?: number;
      };
      orientation?: 'portrait' | 'landscape';
      preprocessOptions?: PreprocessOptions;
    } = {}
  ): Promise<Buffer> {
    const sessionId = options.sessionId || uuidv4().substring(0, 8);
    const autoCleanup = options.autoCleanup !== false; // 默认启用自动清理

    try {
      console.log(`开始HTML转Word转换... 会话ID: ${sessionId}`);

      // 第一步：处理base64图片（转换为临时文件）
      const imageProcessor = createDocxImageProcessor(`temp/images/${sessionId}`);
      const imageProcessedHtml = await imageProcessor.processHtmlImages(htmlContent);

      // 第二步：HTML预处理（转换着重号和波浪线为特殊标记）
      const preprocessedHtml = preprocessHtml(imageProcessedHtml, {
        ...options.preprocessOptions,
        processEmphasisMarks: true, // 转换着重号为特殊标记
        processWavyUnderlines: true, // 转换波浪线为特殊标记
        processImages: true, // 处理图片信息检测
      });

      // 保存预处理后的HTML用于调试（使用唯一文件名避免并发冲突）
      const debugFilePath = saveProcessedHtml(
        preprocessedHtml,
        undefined,
        sessionId
      );
      // 手动跟踪调试文件
      this.fileManagerService.trackSessionFile(sessionId, debugFilePath);
      logConversionInfo('预处理完成', {
        originalLength: htmlContent.length,
        processedLength: preprocessedHtml.length,
      });

      // 第二步：使用html-to-docx进行基础转换
      const docxOptions = {
        table: { row: { cantSplit: true } },
        footer: true,
        pageNumber: true,
        margins: options.margins || this.DEFAULT_MARGINS,
        orientation: options.orientation || 'portrait',
        title: options.title || '文档',
        creator: options.author || '系统',
      };

      const docxBuffer = await HTMLtoDOCX(preprocessedHtml, null, docxOptions);

      // 第三步：XML补丁处理（处理html-to-docx无法处理的特殊样式）
      const finalBuffer = await patchWordDocument(docxBuffer, preprocessedHtml);

      // 第四步：清理临时图片文件
      await imageProcessor.cleanup();

      console.log('HTML转Word转换完成');

      // 自动清理临时文件（如果启用）
      if (autoCleanup) {
        // 延迟清理，确保文件已经被使用完毕
        setTimeout(async () => {
          try {
            await this.fileManagerService.cleanupSessionFiles(sessionId);
            console.log(`会话 ${sessionId} 临时文件已自动清理`);
          } catch (error) {
            console.warn(`自动清理临时文件失败: ${error.message}`);
          }
        }, 5000); // 5秒后清理
      }

      return finalBuffer;
    } catch (error) {
      console.error('HTML转Word转换失败:', error);

      // 即使转换失败也要清理临时文件
      if (autoCleanup) {
        try {
          await this.fileManagerService.cleanupSessionFiles(sessionId);
        } catch (cleanupError) {
          console.warn(`清理失败转换的临时文件失败: ${cleanupError.message}`);
        }
      }

      throw new Error(`HTML转Word转换失败: ${error.message}`);
    }
  }

  /**
   * 将HTML内容转换为Word文档并保存到文件
   * @param htmlContent HTML内容
   * @param outputPath 输出文件路径
   * @param options 导出选项
   * @returns 保存的文件路径
   */
  async exportHtmlToWordFile(
    htmlContent: string,
    outputPath: string,
    options: {
      title?: string;
      author?: string;
      sessionId?: string;
      margins?: {
        top?: number;
        right?: number;
        bottom?: number;
        left?: number;
      };
      orientation?: 'portrait' | 'landscape';
      preprocessOptions?: PreprocessOptions;
    } = {}
  ): Promise<string> {
    try {
      const docxBuffer = await this.exportHtmlToWord(htmlContent, options);
      const outputDir = path.dirname(outputPath);
      if (!fs.existsSync(outputDir)) {
        fs.mkdirSync(outputDir, { recursive: true });
      }
      await fs.promises.writeFile(outputPath, docxBuffer);
      return outputPath;
    } catch (error) {
      console.error('保存Word文档失败:', error);
      throw error;
    }
  }

  /**
   * 从本地HTML文件导出Word文档到临时目录
   * @param htmlFilePath HTML文件路径
   * @param options 导出选项
   * @param sessionId 会话ID（可选，用于区分不同用户）
   * @returns 生成的Word文档路径
   */
  async exportLocalHtmlToTemp(
    htmlFilePath: string,
    options: {
      title?: string;
      author?: string;
      margins?: {
        top?: number;
        right?: number;
        bottom?: number;
        left?: number;
      };
      orientation?: 'portrait' | 'landscape';
      sessionId?: string;
    } = {}
  ): Promise<string> {
    try {
      // 读取HTML文件内容
      const htmlContent = await fs.promises.readFile(htmlFilePath, 'utf-8');

      // 生成唯一输出文件名，避免并发冲突
      const baseName = path.basename(htmlFilePath, path.extname(htmlFilePath));
      const uniqueFileName = this.generateUniqueFileName(
        baseName,
        'docx',
        options.sessionId
      );
      const outputPath = path.join(this.OUTPUT_DIR, uniqueFileName);

      // 转换并保存
      return await this.exportHtmlToWordFile(htmlContent, outputPath, options);
    } catch (error) {
      console.error('从本地HTML文件导出Word失败:', error);
      throw error;
    }
  }
}
