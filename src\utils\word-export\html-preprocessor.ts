/**
 * HTML预处理工具
 * 处理html-to-docx无法很好处理的HTML元素和样式
 */
import { JSDOM } from 'jsdom';

export interface PreprocessOptions {
  /** 是否处理着重号 */
  processEmphasisMarks?: boolean;
  /** 是否处理波浪线 */
  processWavyUnderlines?: boolean;
  /** 是否处理display:inline-block元素 */
  processInlineBlocks?: boolean;
  /** 是否处理浮动元素 */
  processFloatingElements?: boolean;
  /** 是否优化表格样式 */
  optimizeTableStyles?: boolean;
  /** 是否处理图片 */
  processImages?: boolean;
}

/**
 * HTML预处理器
 */
export class HtmlPreprocessor {
  private dom: JSDOM;
  private document: Document;

  constructor(htmlContent: string) {
    this.dom = new JSDOM(htmlContent);
    this.document = this.dom.window.document;
  }

  /**
   * 执行预处理
   */
  preprocess(options: PreprocessOptions = {}): string {
    const {
      processEmphasisMarks = true,
      processWavyUnderlines = true,
      processInlineBlocks = true,
      processFloatingElements = true,
      optimizeTableStyles = true,
      processImages = true,
    } = options;

    console.log('开始HTML预处理...');

    if (processEmphasisMarks) {
      this.processEmphasisMarks();
    }

    if (processWavyUnderlines) {
      this.processWavyUnderlines();
    }

    if (processImages) {
      this.processImages();
    }

    if (processInlineBlocks) {
      this.processInlineBlockElements();
    }

    if (processFloatingElements) {
      this.removeFloatingElements();
    }

    if (optimizeTableStyles) {
      this.optimizeTableStyles();
    }

    // 清理可能导致问题的样式
    this.cleanupProblematicStyles();

    console.log('HTML预处理完成');
    return this.getProcessedHtml();
  }

  /**
   * 处理着重号
   * 使用特殊标记方案：将着重号转换为特殊标记，供后续docx库处理
   */
  private processEmphasisMarks(): void {
    console.log('处理着重号：转换为特殊标记...');

    // 查找所有带有着重号样式的元素
    const emphasisElements = this.document.querySelectorAll(
      '[data-emphasis-mark="dot"], .emphasis-mark, [style*="text-emphasis"]'
    );

    let processedCount = 0;

    emphasisElements.forEach(element => {
      const htmlElement = element as HTMLElement;
      const textContent = htmlElement.textContent?.trim();

      if (textContent) {
        // 转换为特殊标记格式
        htmlElement.innerHTML = `<strong>[EMPHASIS]${textContent}[/EMPHASIS]</strong>`;

        // 移除原有的着重号相关样式和属性
        htmlElement.removeAttribute('data-emphasis-mark');
        htmlElement.classList.remove('emphasis-mark');
        htmlElement.style.removeProperty('text-emphasis');
        htmlElement.style.removeProperty('text-emphasis-position');
        htmlElement.style.removeProperty('text-decoration');
        htmlElement.style.removeProperty('border-bottom');

        processedCount++;
      }
    });

    if (processedCount > 0) {
      console.log(
        `着重号预处理完成，转换了 ${processedCount} 个着重号为特殊标记`
      );
    }
  }

  /**
   * 处理波浪线下划线
   * 使用特殊标记方案：将波浪线转换为特殊标记，供后续docx库处理
   */
  private processWavyUnderlines(): void {
    console.log('处理波浪线：转换为特殊标记...');

    // 查找所有带有波浪线样式的元素
    const wavyElements = this.document.querySelectorAll(
      '[style*="text-decoration: underline wavy"], [style*="text-decoration:underline wavy"], .wavy-underline'
    );

    let processedCount = 0;

    wavyElements.forEach(element => {
      const htmlElement = element as HTMLElement;
      const textContent = htmlElement.textContent?.trim();

      if (textContent) {
        // 转换为特殊标记格式
        htmlElement.innerHTML = `<strong>[WAVY]${textContent}[/WAVY]</strong>`;

        // 移除原有的波浪线相关样式和属性
        htmlElement.classList.remove('wavy-underline');
        htmlElement.style.removeProperty('text-decoration');

        processedCount++;
      }
    });

    if (processedCount > 0) {
      console.log(
        `波浪线预处理完成，转换了 ${processedCount} 个波浪线为特殊标记`
      );
    }
  }

  /**
   * 处理图片
   * 保持原始尺寸，让html-to-docx库直接处理图片
   */
  private processImages(): void {
    console.log('处理图片：保持原始尺寸，让html-to-docx库直接处理...');

    const images = this.document.querySelectorAll('img');
    let base64Count = 0;

    images.forEach(img => {
      const htmlImg = img as HTMLImageElement;
      const src = htmlImg.src;

      // 检查是否是base64图片
      if (src && src.startsWith('data:image/')) {
        base64Count++;
        console.log(`处理base64图片 ${base64Count}:`, {
          type: src.substring(5, src.indexOf(';')),
          size: src.length,
          width: htmlImg.width || '未指定',
          height: htmlImg.height || '未指定',
          alt: htmlImg.alt || '无alt属性'
        });
      }

      // 确保图片有alt属性（有助于Word处理）
      if (!htmlImg.alt) {
        htmlImg.alt = '图片';
      }

      // 不修改任何尺寸，保持原始状态
      console.log(`保持图片原始尺寸: ${htmlImg.width || '未指定'}x${htmlImg.height || '未指定'}`);
    });

    console.log(`图片预处理完成: 发现 ${images.length} 个图片，其中 ${base64Count} 个base64图片，保持原始尺寸`);
    console.log('图片将由html-to-docx库直接处理');
  }

  /**
   * 处理display:inline-block元素
   * 确保这些元素在Word中正确显示
   */
  private processInlineBlockElements(): void {
    console.log('处理inline-block元素...');

    const inlineBlockElements = this.document.querySelectorAll('*');
    let inlineBlockCount = 0;

    inlineBlockElements.forEach(element => {
      const htmlElement = element as HTMLElement;
      const style = htmlElement.style;

      if (style.display === 'inline-block') {
        // 保持inline-block显示，不进行强制转换
        // inline-block在html-to-docx中通常能正确处理
        // 移除之前的强制转换逻辑，因为它会破坏原有布局
        inlineBlockCount++;
      }
    });

    if (inlineBlockCount > 0) {
      console.log(`处理了 ${inlineBlockCount} 个inline-block元素`);
    }
  }

  /**
   * 移除浮动元素
   * position:absolute等浮动元素在Word转换中可能造成问题
   */
  private removeFloatingElements(): void {
    console.log('处理浮动元素...');

    const floatingElements = this.document.querySelectorAll('*');
    let removedCount = 0;

    floatingElements.forEach(element => {
      const htmlElement = element as HTMLElement;
      const style = htmlElement.style;

      if (style.position === 'absolute' || style.position === 'fixed') {
        // 移除浮动定位，避免在Word转换中造成布局问题
        style.removeProperty('position');
        style.removeProperty('top');
        style.removeProperty('left');
        style.removeProperty('right');
        style.removeProperty('bottom');

        removedCount++;
      }
    });

    if (removedCount > 0) {
      console.log(`移除了 ${removedCount} 个浮动定位样式`);
    }
  }

  /**
   * 优化表格样式
   * 彻底修复双线边框问题，确保单线边框
   */
  private optimizeTableStyles(): void {
    console.log('优化表格样式...');

    const tables = this.document.querySelectorAll('table');

    tables.forEach(table => {
      const htmlTable = table as HTMLTableElement;

      // 彻底清理表格样式，重新设置
      htmlTable.removeAttribute('border');
      htmlTable.removeAttribute('cellpadding');
      htmlTable.removeAttribute('cellspacing');

      // 关键设置：确保边框合并
      htmlTable.style.borderCollapse = 'collapse';
      htmlTable.style.borderSpacing = '0';

      // 移除表格本身的所有边框样式
      htmlTable.style.removeProperty('border');
      htmlTable.style.removeProperty('border-top');
      htmlTable.style.removeProperty('border-bottom');
      htmlTable.style.removeProperty('border-left');
      htmlTable.style.removeProperty('border-right');

      // 设置表格宽度
      if (!htmlTable.style.width) {
        htmlTable.style.width = '100%';
      }

      // 处理表格单元格，确保单线边框
      const cells = htmlTable.querySelectorAll('td, th');
      cells.forEach(cell => {
        const htmlCell = cell as HTMLTableCellElement;

        // 彻底清理单元格属性
        htmlCell.removeAttribute('width');
        htmlCell.removeAttribute('height');
        htmlCell.removeAttribute('border');
        htmlCell.removeAttribute('cellpadding');
        htmlCell.removeAttribute('cellspacing');

        // 清理所有边框样式
        htmlCell.style.removeProperty('border-top');
        htmlCell.style.removeProperty('border-bottom');
        htmlCell.style.removeProperty('border-left');
        htmlCell.style.removeProperty('border-right');
        htmlCell.style.removeProperty('border-width');
        htmlCell.style.removeProperty('border-style');
        htmlCell.style.removeProperty('border-color');

        // 重新设置单一边框 - 这是关键
        htmlCell.style.border = '1px solid black';

        // 设置内边距
        htmlCell.style.padding = '6px 8px';

        // 设置垂直对齐
        htmlCell.style.verticalAlign = 'top';

        // 确保文本换行正常
        htmlCell.style.overflowWrap = 'break-word';
      });
    });
  }

  /**
   * 清理可能导致问题的样式
   */
  private cleanupProblematicStyles(): void {
    console.log('清理问题样式...');

    const allElements = this.document.querySelectorAll('*');

    allElements.forEach(element => {
      const htmlElement = element as HTMLElement;
      const style = htmlElement.style;

      // 清理可能导致XML问题的样式属性
      const problematicProperties = [
        'white-space-collapse',
        'overflow-wrap',
        'word-break',
        'max-width',
      ];

      problematicProperties.forEach(prop => {
        if (style.getPropertyValue(prop)) {
          style.removeProperty(prop);
        }
      });

      // 清理可能有问题的类名
      if (htmlElement.className) {
        // 移除可能导致问题的类
        const problematicClasses = ['MsoNormal', 'double-underline'];
        problematicClasses.forEach(className => {
          htmlElement.classList.remove(className);
        });
      }

      // 清理align属性（使用style代替）
      if (htmlElement.hasAttribute('align')) {
        const alignValue = htmlElement.getAttribute('align');
        htmlElement.removeAttribute('align');
        if (alignValue && !style.textAlign) {
          style.textAlign = alignValue;
        }
      }

      // 特别处理表格单元格的width属性
      if (htmlElement.tagName === 'TD' || htmlElement.tagName === 'TH') {
        // 移除width属性，这可能导致XML问题
        htmlElement.removeAttribute('width');

        // 清理所有可能有问题的宽度样式
        if (style.width) {
          // 移除所有宽度样式，让表格自动调整
          style.removeProperty('width');
        }
      }

      // 特别处理表格的样式
      if (htmlElement.tagName === 'TABLE') {
        // 清理表格的高度和像素宽度
        if (style.height) {
          style.removeProperty('height');
        }
        if (style.width && style.width.includes('px')) {
          style.width = '100%';
        }
      }
    });
  }

  /**
   * 获取处理后的HTML
   */
  private getProcessedHtml(): string {
    return this.dom.serialize();
  }
}

/**
 * 预处理HTML内容
 * @param htmlContent 原始HTML内容
 * @param options 预处理选项
 * @returns 预处理后的HTML内容
 */
export function preprocessHtml(
  htmlContent: string,
  options: PreprocessOptions = {}
): string {
  const preprocessor = new HtmlPreprocessor(htmlContent);
  return preprocessor.preprocess(options);
}
