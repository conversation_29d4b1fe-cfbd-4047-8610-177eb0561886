/**
 * 使用docx库处理图片的处理器
 * 解决html-to-docx库不支持base64图片的问题
 */

import * as fs from 'fs';
import * as path from 'path';

export interface ImageInfo {
  id: string;
  src: string;
  alt: string;
  width?: number;
  height?: number;
  type: 'base64' | 'url';
  mimeType?: string;
  data?: string;
}

export class DocxImageProcessor {
  private images: ImageInfo[] = [];
  private tempDir: string;

  constructor(tempDir: string = 'temp/images') {
    this.tempDir = tempDir;
    this.ensureTempDir();
  }

  /**
   * 确保临时目录存在
   */
  private ensureTempDir(): void {
    if (!fs.existsSync(this.tempDir)) {
      fs.mkdirSync(this.tempDir, { recursive: true });
    }
  }

  /**
   * 从HTML中提取图片并转换base64为临时文件
   */
  async processHtmlImages(htmlContent: string): Promise<string> {
    console.log('开始处理HTML中的图片...');
    
    // 提取图片信息
    this.extractImages(htmlContent);
    
    if (this.images.length === 0) {
      console.log('未发现图片');
      return htmlContent;
    }

    console.log(`发现 ${this.images.length} 个图片`);
    
    let processedHtml = htmlContent;
    
    // 处理每个图片
    for (const imageInfo of this.images) {
      if (imageInfo.type === 'base64') {
        try {
          // 将base64图片保存为临时文件
          const tempFilePath = await this.saveBase64AsFile(imageInfo);
          
          // 替换HTML中的base64 src为文件路径
          const originalImgRegex = new RegExp(
            `<img([^>]*?)src="${imageInfo.src.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}"([^>]*?)>`,
            'gi'
          );
          
          const fileUrl = `file:///${tempFilePath.replace(/\\/g, '/')}`;
          const replacement = `<img$1src="${fileUrl}"$2>`;
          processedHtml = processedHtml.replace(originalImgRegex, replacement);

          console.log(`✅ 已转换base64图片:`);
          console.log(`   原始src长度: ${imageInfo.src.length}字符`);
          console.log(`   临时文件路径: ${tempFilePath}`);
          console.log(`   文件URL: ${fileUrl}`);
          console.log(`   文件大小: ${fs.statSync(tempFilePath).size}字节`);
        } catch (error) {
          console.error(`处理图片失败: ${imageInfo.alt}`, error);
        }
      }
    }
    
    console.log('HTML图片处理完成');
    return processedHtml;
  }

  /**
   * 从HTML中提取图片信息
   */
  private extractImages(htmlContent: string): void {
    const imgRegex = /<img[^>]*>/gi;
    const matches = htmlContent.match(imgRegex) || [];
    
    this.images = [];
    
    matches.forEach((imgTag, index) => {
      // 提取src属性
      const srcMatch = imgTag.match(/src\s*=\s*["']([^"']+)["']/i);
      if (!srcMatch) return;
      
      const src = srcMatch[1];
      
      // 提取alt属性
      const altMatch = imgTag.match(/alt\s*=\s*["']([^"']*)["']/i);
      const alt = altMatch ? altMatch[1] : '图片';
      
      // 提取width属性
      const widthMatch = imgTag.match(/width\s*=\s*["']?(\d+)["']?/i);
      const width = widthMatch ? parseInt(widthMatch[1]) : undefined;
      
      // 提取height属性
      const heightMatch = imgTag.match(/height\s*=\s*["']?(\d+)["']?/i);
      const height = heightMatch ? parseInt(heightMatch[1]) : undefined;
      
      const imageInfo: ImageInfo = {
        id: `IMAGE_${index}_${Date.now()}`,
        src: src,
        alt: alt,
        width: width,
        height: height,
        type: src.startsWith('data:image/') ? 'base64' : 'url'
      };
      
      // 解析base64图片信息
      if (imageInfo.type === 'base64') {
        const match = src.match(/^data:image\/([^;]+);base64,(.+)$/);
        if (match) {
          imageInfo.mimeType = match[1];
          imageInfo.data = match[2];
        }
      }
      
      this.images.push(imageInfo);
    });
  }

  /**
   * 将base64图片保存为临时文件
   */
  private async saveBase64AsFile(imageInfo: ImageInfo): Promise<string> {
    if (!imageInfo.data || !imageInfo.mimeType) {
      throw new Error('无效的base64图片数据');
    }

    const extension = imageInfo.mimeType === 'png' ? 'png' : 'jpg';
    const fileName = `${imageInfo.id}.${extension}`;
    const filePath = path.join(this.tempDir, fileName);
    
    // 将base64转换为Buffer
    const imageBuffer = Buffer.from(imageInfo.data, 'base64');
    
    // 保存文件
    await fs.promises.writeFile(filePath, imageBuffer);
    
    return path.resolve(filePath);
  }

  /**
   * 清理临时文件
   */
  async cleanup(): Promise<void> {
    try {
      if (fs.existsSync(this.tempDir)) {
        const files = await fs.promises.readdir(this.tempDir);
        for (const file of files) {
          const filePath = path.join(this.tempDir, file);
          await fs.promises.unlink(filePath);
        }
        console.log(`清理了 ${files.length} 个临时图片文件`);
      }
    } catch (error) {
      console.error('清理临时文件失败:', error);
    }
  }

  /**
   * 获取处理的图片信息
   */
  getImages(): ImageInfo[] {
    return this.images;
  }
}

/**
 * 创建docx图片处理器
 */
export function createDocxImageProcessor(tempDir?: string): DocxImageProcessor {
  return new DocxImageProcessor(tempDir);
}
