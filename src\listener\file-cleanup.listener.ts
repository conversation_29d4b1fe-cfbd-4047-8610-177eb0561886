import { Provide, Scope, ScopeEnum, Inject } from '@midwayjs/core';
import { DataListener } from '@midwayjs/core';
import { FileManagerService } from '../service/file-manager.service';

/**
 * 文件清理监听器
 * 用于定期清理过期的临时文件
 */
@Provide()
@Scope(ScopeEnum.Singleton)
export class FileCleanupListener extends DataListener<void> {
  private readonly FILE_LIFETIME = 60 * 60 * 1000; // 1小时
  private intervalHandler: NodeJS.Timeout;

  @Inject()
  fileManagerService: FileManagerService;

  /**
   * 初始化数据
   */
  async initData() {
    console.log('文件清理监听器初始化...');
    // 初始执行一次清理
    await this.cleanupFiles();
  }

  /**
   * 数据订阅更新
   */
  onData(setData) {
    // 每小时执行一次清理
    this.intervalHandler = setInterval(async () => {
      await this.cleanupFiles();
      setData(); // 更新数据状态
    }, this.FILE_LIFETIME);
    console.log('文件清理定时任务已启动，每小时执行一次');
  }

  /**
   * 清理过期文件
   */
  private async cleanupFiles() {
    try {
      console.log('开始清理过期文件...');
      await this.fileManagerService.cleanupAllExpiredFiles(this.FILE_LIFETIME);
      console.log('过期文件清理完成');
    } catch (error) {
      console.error('清理过期文件失败:', error);
    }
  }

  /**
   * 清理资源
   */
  async destroyListener() {
    if (this.intervalHandler) {
      clearInterval(this.intervalHandler);
    }
  }
}
