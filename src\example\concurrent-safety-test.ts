/**
 * 并发安全测试示例
 * 模拟多用户同时调用导入导出功能，验证文件冲突问题是否已解决
 */

import { WordExportService } from '../service/word-export.service';
import { FileManagerService } from '../service/file-manager.service';
import * as fs from 'fs';
import * as path from 'path';

/**
 * 模拟并发导出测试
 */
async function testConcurrentExport() {
  console.log('🚀 开始并发安全测试...');

  const wordExportService = new WordExportService();
  const fileManagerService = new FileManagerService();

  // 模拟HTML内容
  const htmlContent = `
    <html>
      <head><title>测试文档</title></head>
      <body>
        <h1>并发测试文档</h1>
        <p>这是一个用于测试并发安全的文档。</p>
        <p style="text-emphasis: filled currentColor; text-emphasis-position: under right;">
          这段文字有着重号。
        </p>
        <table border="1">
          <tr><th>列1</th><th>列2</th></tr>
          <tr><td>数据1</td><td>数据2</td></tr>
        </table>
      </body>
    </html>
  `;

  // 模拟多个用户同时调用
  const userCount = 5;
  const promises: Promise<string>[] = [];

  console.log(`📊 模拟 ${userCount} 个用户同时导出文档...`);

  for (let i = 0; i < userCount; i++) {
    const sessionId = fileManagerService.generateSessionId();
    const promise = wordExportService
      .exportHtmlToWord(htmlContent, {
        title: `用户${i + 1}的文档`,
        author: `用户${i + 1}`,
        sessionId,
      })
      .then(buffer => {
        // 保存到唯一路径
        const outputPath = fileManagerService.getUniqueWordExportPath(
          `user${i + 1}_document`,
          sessionId
        );
        return fileManagerService
          .writeFileSync(outputPath, buffer)
          .then(() => outputPath);
      });

    promises.push(promise);
  }

  try {
    // 等待所有导出完成
    const results = await Promise.all(promises);

    console.log('✅ 所有导出任务完成！');
    console.log('📁 生成的文件：');
    results.forEach((filePath, index) => {
      console.log(`  用户${index + 1}: ${path.basename(filePath)}`);
    });

    // 验证文件是否都存在且不同
    const fileStats = results.map(filePath => ({
      path: filePath,
      exists: fs.existsSync(filePath),
      size: fs.existsSync(filePath) ? fs.statSync(filePath).size : 0,
    }));

    console.log('\n📋 文件验证结果：');
    fileStats.forEach((stat, index) => {
      console.log(
        `  用户${index + 1}: 存在=${stat.exists}, 大小=${stat.size}字节`
      );
    });

    // 检查是否有重复文件名
    const fileNames = results.map(filePath => path.basename(filePath));
    const uniqueNames = new Set(fileNames);

    if (fileNames.length === uniqueNames.size) {
      console.log('✅ 所有文件名都是唯一的，没有冲突！');
    } else {
      console.log('❌ 发现文件名冲突！');
    }

    return results;
  } catch (error) {
    console.error('❌ 并发测试失败:', error);
    throw error;
  }
}

/**
 * 测试调试文件的并发安全
 */
async function testConcurrentDebugFiles() {
  console.log('\n🔍 测试调试文件并发安全...');

  const fileManagerService = new FileManagerService();
  const debugContent = '<html><body><h1>调试内容</h1></body></html>';

  const promises = [];
  for (let i = 0; i < 3; i++) {
    const sessionId = fileManagerService.generateSessionId();
    const promise = (async () => {
      const debugPath = fileManagerService.getUniqueDebugFilePath(
        'debug-test',
        'html',
        sessionId
      );
      await fileManagerService.writeFileSync(debugPath, debugContent);
      return debugPath;
    })();
    promises.push(promise);
  }

  const debugFiles = await Promise.all(promises);
  console.log('📁 生成的调试文件：');
  debugFiles.forEach((filePath, index) => {
    console.log(`  调试文件${index + 1}: ${path.basename(filePath)}`);
  });

  return debugFiles;
}

/**
 * 清理测试文件
 */
async function cleanupTestFiles(filePaths: string[]) {
  console.log('\n🧹 清理测试文件...');

  const fileManagerService = new FileManagerService();

  for (const filePath of filePaths) {
    try {
      await fileManagerService.deleteFile(filePath);
    } catch (error) {
      console.warn(`清理文件失败: ${filePath}`, error);
    }
  }

  console.log('✅ 测试文件清理完成');
}

/**
 * 主测试函数
 */
async function main() {
  try {
    console.log('🎯 并发安全测试开始');
    console.log('='.repeat(50));

    // 测试并发导出
    const exportFiles = await testConcurrentExport();

    // 测试调试文件并发
    const debugFiles = await testConcurrentDebugFiles();

    console.log('\n' + '='.repeat(50));
    console.log('🎉 所有测试通过！并发安全问题已解决。');

    // 询问是否清理测试文件
    console.log('\n💡 提示：测试文件已生成，可以手动检查文件内容。');
    console.log('   如需清理测试文件，请运行清理函数。');
    console.log(`   导出文件数量: ${exportFiles.length}`);
    console.log(`   调试文件数量: ${debugFiles.length}`);

    // 可选：自动清理测试文件（取消注释以启用）
    // await cleanupTestFiles([...exportFiles, ...debugFiles]);
  } catch (error) {
    console.error('❌ 测试失败:', error);
    process.exit(1);
  }
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
  main();
}

export {
  testConcurrentExport,
  testConcurrentDebugFiles,
  cleanupTestFiles,
  main as runConcurrentSafetyTest,
};
