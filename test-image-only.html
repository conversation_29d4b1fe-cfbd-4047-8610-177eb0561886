<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>图片测试</title>
</head>
<body>
    <h1>图片显示测试</h1>
    
    <h2>小图片测试</h2>
    <p>这是一个1x1像素的红色图片：</p>
    <img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==" width="50" height="50" alt="红色像素">
    
    <h2>中等图片测试</h2>
    <p>这是一个100x100像素的蓝色图片：</p>
    <img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==" width="100" height="100" alt="蓝色像素">
    
    <h2>大图片测试</h2>
    <p>这是一个较大的图片（应该被自动调整尺寸）：</p>
    <img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==" width="800" height="600" alt="大图片">
    
    <h2>无尺寸图片测试</h2>
    <p>这是一个没有指定尺寸的图片：</p>
    <img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==" alt="无尺寸图片">
</body>
</html>
