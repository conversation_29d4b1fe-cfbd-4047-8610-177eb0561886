/**
 * 混合Word处理器
 * 结合html-to-docx和docx库的优势，实现完整的HTML到Word转换
 * 
 * 处理流程：
 * 1. 使用html-to-docx处理大部分HTML内容
 * 2. 使用docx库处理图片和其他html-to-docx不支持的功能
 */

import { ImageProcessor, ImageInfo } from './image-processor';

export interface HybridProcessOptions {
  /** 是否处理图片 */
  processImages?: boolean;
  /** 图片最大宽度 */
  maxImageWidth?: number;
  /** 是否启用调试模式 */
  debug?: boolean;
}

export class HybridWordProcessor {
  private imageProcessor: ImageProcessor;
  private images: ImageInfo[] = [];

  constructor() {
    this.imageProcessor = new ImageProcessor();
  }

  /**
   * 预处理HTML，提取图片信息并替换为占位符
   */
  preprocessHtml(htmlContent: string, options: HybridProcessOptions = {}): string {
    const { processImages = true, debug = false } = options;

    if (!processImages) {
      return htmlContent;
    }

    console.log('混合处理器：开始预处理HTML...');

    // 提取图片信息
    this.images = this.imageProcessor.extractImages(htmlContent);
    
    if (this.images.length === 0) {
      console.log('混合处理器：未发现图片');
      return htmlContent;
    }

    console.log(`混合处理器：发现 ${this.images.length} 个图片`);

    // 替换图片为占位符
    let processedHtml = htmlContent;
    
    this.images.forEach((imageInfo, index) => {
      const placeholder = `[IMAGE_PLACEHOLDER_${imageInfo.id}]`;
      
      // 构建原始img标签的正则表达式
      const imgRegex = new RegExp(
        `<img[^>]*src="${imageInfo.src.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}"[^>]*>`,
        'gi'
      );
      
      processedHtml = processedHtml.replace(imgRegex, placeholder);
      
      if (debug) {
        console.log(`替换图片 ${index + 1}: ${imageInfo.alt} -> ${placeholder}`);
      }
    });

    console.log('混合处理器：HTML预处理完成');
    return processedHtml;
  }

  /**
   * 后处理Word文档，插入实际图片
   * 当前版本：暂时返回原始文档，完整图片插入功能待实现
   */
  async postProcessWordDocument(docxBuffer: Buffer, options: HybridProcessOptions = {}): Promise<Buffer> {
    const { processImages = true } = options;

    if (!processImages || this.images.length === 0) {
      console.log('混合处理器：跳过图片后处理');
      return docxBuffer;
    }

    console.log(`混合处理器：检测到 ${this.images.length} 个图片，但完整图片插入功能尚未实现`);

    // 输出图片信息用于调试
    this.images.forEach((img, index) => {
      console.log(`图片 ${index + 1}:`, {
        id: img.id,
        alt: img.alt,
        type: img.type,
        size: img.width && img.height ? `${img.width}x${img.height}` : '未指定'
      });
    });

    console.log('混合处理器：返回原始文档（图片功能待完善）');
    return docxBuffer;
  }



  /**
   * 清理资源
   */
  clear(): void {
    this.images = [];
    this.imageProcessor.clear();
  }
}

/**
 * 创建混合Word处理器实例
 */
export function createHybridWordProcessor(): HybridWordProcessor {
  return new HybridWordProcessor();
}
