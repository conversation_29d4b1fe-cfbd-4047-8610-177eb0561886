<!DOCTYPE html><html><head>
    <meta charset="UTF-8">
    <title>简单图片测试</title>
</head>
<body>
    <h1>图片显示测试</h1>

    <h2>测试1：简单文本</h2>
    <p>这是一段普通文本，用来确认基本转换功能正常。</p>

    <h2>测试2：表格中的图片（模仿GitHub issue）</h2>
    <table style="border-collapse: collapse; border-spacing: 0; width: 100%;">
        <tbody><tr>
            <td style="border: 1px solid black; padding: 6px 8px; vertical-align: top;">
                <img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==" width="100" height="100" alt="红色像素" style="; max-width: 450px; height: auto;">
            </td>
            <td style="border: 1px solid black; padding: 6px 8px; vertical-align: top;">
                <img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==" width="100" height="100" alt="蓝色像素" style="; max-width: 450px; height: auto;">
            </td>
        </tr>
    </tbody></table>

    <h2>测试3：段落中的图片</h2>
    <p>这是一个段落，包含一个图片：<img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==" width="20" height="20" alt="小图片" style="; max-width: 450px; height: auto;">，图片应该显示在这里。</p>

    <p>测试完成。</p>

</body></html>